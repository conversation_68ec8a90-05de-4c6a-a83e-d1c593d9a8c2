#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
canal_a_dataset.py — De un canal/playlist/video de YouTube a dataset de clips de audio + subtítulos
Uso:
  ./canal_a_dataset.py <URL_CANAL_O_PLAYLIST_O_VIDEO> <SALIDA_DIR> [--demucs] [--denoise] [--min-sec 0.8] [--max-sec 12] [--sr 16000] [--lang es] [--model small]
Requisitos:
  apt-get install -y ffmpeg
  pip install yt-dlp webvtt-py pandas tqdm rich openai-whisper demucs
Notas:
  - Whisper/Demucs usan CUDA automáticamente si está disponible (PyTorch con CUDA instalado).
  - Para --denoise (arnndn) coloca un modelo .rnnn en ./rnnoise-models/mastering.rnnn o cambia la ruta.
"""

import json, re, subprocess, sys, os, shutil, csv
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# Check for required dependencies
missing_deps = []
try:
    from tqdm import tqdm
except ImportError:
    missing_deps.append("tqdm")
    # Fallback for tqdm
    def tqdm(iterable, **kwargs):
        return iterable

try:
    from rich import print
except ImportError:
    missing_deps.append("rich")
    # Fallback for rich print
    def print(*args, **kwargs):
        import builtins
        builtins.print(*args, **kwargs)

try:
    import webvtt
except ImportError:
    missing_deps.append("webvtt-py")

try:
    import pandas as pd
except ImportError:
    missing_deps.append("pandas")

# --- Detección de CUDA para Whisper/Demucs ---
try:
    import torch
    HAS_CUDA = torch.cuda.is_available()
except Exception:
    HAS_CUDA = False

# --- Binaries ---
def which(name, default=None):
    p = shutil.which(name)
    return p if p else (default or name)

YTDLP = which("yt-dlp", "yt-dlp")
FFMPEG = which("ffmpeg", "ffmpeg")

# --- Config por defecto (override por CLI) ---
MIN_SEC = 0.8
MAX_SEC = 12.0
TARGET_SR = 16000
LANG_SUBS = "es"
WHISPER_MODEL = "small"
USE_DEMUCS = False
USE_DENOISE = False
LOUDNORM = True
ARNNDN_MODEL = Path("./rnnoise-models/mastering.rnnn")  # cambia si quieres

# --- Regex de limpieza (reporte de números/símbolos) ---
RX_NUM = re.compile(r"\d")
RX_SYM = re.compile(r"[€$%#@−–—_=+<>^~|\\/*\[\]{}()§°ºª♂♀©®™☎✉✓✔★☆•♪♫¿¡]")

def run(cmd: List[str], check=True, capture=False) -> str:
    """Ejecuta comando y retorna stdout si capture=True."""
    try:
        res = subprocess.run(cmd, check=check, text=True, capture_output=capture)
        return res.stdout if capture else ""
    except subprocess.CalledProcessError as e:
        if capture:
            return e.stdout or ""
        raise

def parse_args(argv: List[str]) -> Dict[str, Any]:
    """Parseo simple de CLI."""
    global USE_DEMUCS, USE_DENOISE, MIN_SEC, MAX_SEC, TARGET_SR, LANG_SUBS, WHISPER_MODEL
    if len(argv) < 3:
        print("[red]Uso: canal_a_dataset.py <URL_CANAL/PLAYLIST/VIDEO> <SALIDA_DIR> [--demucs] [--denoise] [--min-sec 0.8] [--max-sec 12] [--sr 16000] [--lang es] [--model small][/red]")
        sys.exit(1)
    url = argv[1]
    outdir = Path(argv[2])

    i = 3
    while i < len(argv):
        tok = argv[i]
        if tok == "--demucs":
            USE_DEMUCS = True
        elif tok == "--denoise":
            USE_DENOISE = True
        elif tok == "--min-sec":
            i += 1; MIN_SEC = float(argv[i])
        elif tok == "--max-sec":
            i += 1; MAX_SEC = float(argv[i])
        elif tok == "--sr":
            i += 1; TARGET_SR = int(argv[i])
        elif tok == "--lang":
            i += 1; LANG_SUBS = argv[i]
        elif tok == "--model":
            i += 1; WHISPER_MODEL = argv[i]
        else:
            print(f"[yellow]Aviso: argumento desconocido {tok}[/yellow]")
        i += 1

    return {"url": url, "outdir": outdir}

def yt_json(url: str) -> Dict[str, Any]:
    """Devuelve JSON de yt-dlp -J."""
    out = run([YTDLP, "-J", url], capture=True)
    if not out.strip():
        # fallback para canales/feeds grandes
        out = run([YTDLP, "--flat-playlist", "-J", url], capture=True)
    return json.loads(out)

def list_targets(url: str) -> List[str]:
    """
    Devuelve lista de URLs de videos:
      - Si es video único -> lista de 1
      - Si es canal/playlist -> todos los entries
    Incluye Shorts (no filtramos por duración).
    """
    data = yt_json(url)
    urls = []
    if "entries" in data and data["entries"]:
        for e in data["entries"]:
            vid = e.get("url") or e.get("id")
            if not vid: 
                continue
            if not str(vid).startswith("http"):
                urls.append(f"https://www.youtube.com/watch?v={vid}")
            else:
                urls.append(vid)
    else:
        # video único
        if url.startswith("http"):
            urls.append(url)
        else:
            urls.append(f"https://www.youtube.com/watch?v={url}")
    # dedup
    urls = list(dict.fromkeys(urls))
    return urls

def ensure_audio(video_url: str, workdir: Path) -> Path:
    """Descarga solo audio WAV y devuelve la ruta."""
    workdir.mkdir(parents=True, exist_ok=True)
    outtmpl = str(workdir / "%(id)s.%(ext)s")
    run([YTDLP, "-x", "--audio-format", "wav", "-o", outtmpl, video_url])
    wavs = list(workdir.glob("*.wav"))
    if not wavs:
        raise RuntimeError("No se obtuvo WAV")
    return wavs[0]

def try_download_subs(video_url: str, workdir: Path) -> Optional[Path]:
    """Intenta descargar subtítulos normales y, si no hay, auto-subs."""
    outtmpl = str(workdir / "%(id)s.%(ext)s")
    # Subidos por el autor
    try:
        run([YTDLP, "--write-subs", "--sub-lang", LANG_SUBS, "--skip-download", "-o", outtmpl, video_url])
    except Exception:
        pass
    vtts = list(workdir.glob("*.vtt"))
    if vtts:
        return vtts[0]
    # Auto-subs
    try:
        run([YTDLP, "--write-auto-subs", "--sub-lang", LANG_SUBS, "--skip-download", "-o", outtmpl, video_url])
    except Exception:
        pass
    vtts = list(workdir.glob("*.vtt"))
    return vtts[0] if vtts else None

def whisper_vtt(audio_path: Path, vtt_out: Path):
    """Genera .vtt con Whisper. Usa GPU si está disponible."""
    device = "cuda" if HAS_CUDA else "cpu"
    fp16_flag = "True" if HAS_CUDA else "False"
    cmd = [
        "whisper",
        str(audio_path),
        "--model", WHISPER_MODEL,
        "--language", LANG_SUBS,
        "--task", "transcribe",
        "--output_format", "vtt",
        "--device", device,
        "--fp16", fp16_flag
    ]
    run(cmd)
    gen = audio_path.with_suffix(".vtt")
    if not gen.exists():
        raise RuntimeError("Whisper no generó VTT")
    gen.replace(vtt_out)

def ffmpeg_base_filters() -> str:
    """Cadena de filtros de audio para ffmpeg."""
    chain = []
    if LOUDNORM:
        chain.append("loudnorm=I=-23:LRA=7:TP=-2")
    if USE_DENOISE and ARNNDN_MODEL.exists():
        chain.append(f"arnndn=m={ARNNDN_MODEL}")
    chain.append(f"aformat=channel_layouts=mono,aresample={TARGET_SR}")
    return ",".join(chain)

def cut_clip(input_wav: Path, start: float, end: float, out_wav: Path) -> bool:
    """Recorta un clip con ffmpeg entre start/end; devuelve True si se creó."""
    dur = max(0.0, end - start)
    if dur < MIN_SEC or dur > MAX_SEC:
        return False
    af = ffmpeg_base_filters()
    cmd = [
        FFMPEG, "-y",
        "-ss", f"{start:.3f}",
        "-to", f"{end:.3f}",
        "-i", str(input_wav),
        "-af", af,
        "-ar", str(TARGET_SR),
        "-ac", "1",
        str(out_wav)
    ]
    run(cmd)
    return out_wav.exists() and out_wav.stat().st_size > 0

def maybe_demucs(input_wav: Path) -> Path:
    """Si --demucs, separa voz y devuelve vocals.wav; si falla, devuelve el original."""
    if not USE_DEMUCS:
        return input_wav
    device = "cuda" if HAS_CUDA else "cpu"
    try:
        run(["demucs", "-d", device, "--two-stems", "vocals", str(input_wav)])
        stem = Path("separated") / "htdemucs" / input_wav.stem / "vocals.wav"
        return stem if stem.exists() else input_wav
    except Exception:
        return input_wav

def parse_vtt_to_segments(vtt_path: Path) -> List[Dict[str, Any]]:
    """Convierte VTT a lista de segmentos (start, end, text)."""
    segs: List[Dict[str, Any]] = []
    def hhmmss_to_s(ts: str) -> float:
        h, m, s = ts.split(":")
        return int(h)*3600 + int(m)*60 + float(s)
    for i, cap in enumerate(webvtt.read(str(vtt_path))):
        text = re.sub(r"\s+", " ", cap.text).strip()
        if not text:
            continue
        start = hhmmss_to_s(cap.start)
        end = hhmmss_to_s(cap.end)
        segs.append({"idx": i+1, "start": start, "end": end, "text": text})
    return segs

def process_one_video(vurl: str, clips_dir: Path, meta_rows: List[Dict[str, Any]], report_rows: List[Dict[str, Any]]):
    """Pipeline para un video."""
    # Carpeta temporal por video (timestamp para evitar choques)
    vid_dir = Path(".work") / ("v_" + datetime.now().strftime("%Y%m%d_%H%M%S_%f"))
    vid_dir.mkdir(parents=True, exist_ok=True)

    video_id = "unknown"
    clips_created = 0

    try:
        print(f"[blue]Procesando:[/blue] {vurl}")

        # Audio
        wav = ensure_audio(vurl, vid_dir)
        video_id = wav.stem
        print(f"[green]Audio descargado:[/green] {video_id}")

        # Subtítulos o Whisper
        vtt = try_download_subs(vurl, vid_dir)
        if vtt is None:
            print(f"[yellow]No hay subtítulos, usando Whisper...[/yellow]")
            vtt = vid_dir / "auto.vtt"
            whisper_vtt(wav, vtt)
        else:
            print(f"[green]Subtítulos encontrados[/green]")

        # (Opcional) separación de voz
        wav_for_cuts = maybe_demucs(wav)
        if wav_for_cuts != wav:
            print(f"[green]Separación de voz completada[/green]")

        # Segmentar y cortar
        segs = parse_vtt_to_segments(vtt)
        print(f"[cyan]Segmentos encontrados:[/cyan] {len(segs)}")

        for s in segs:
            start, end, text = s["start"], s["end"], s["text"]
            clip_name = f"{video_id}_{s['idx']:04d}.wav"
            clip_path = clips_dir / clip_name
            ok = cut_clip(wav_for_cuts, start, end, clip_path)
            if not ok:
                continue

            clips_created += 1
            meta_rows.append({
                "file": clip_name,
                "text": text,
                "start": round(start, 3),
                "end": round(end, 3),
                "video_url": vurl,
                "video_id": video_id,
                "device": "cuda" if HAS_CUDA else "cpu"
            })
            has_num = int(bool(RX_NUM.search(text)))
            has_sym = int(bool(RX_SYM.search(text)))
            if has_num or has_sym:
                report_rows.append({
                    "file": clip_name,
                    "text": text,
                    "has_number": has_num,
                    "has_symbol": has_sym,
                    "video_id": video_id
                })

        print(f"[green]Completado:[/green] {clips_created} clips creados para {video_id}")

    except Exception as e:
        print(f"[red]Error procesando {video_id}: {e}[/red]")
        raise
    finally:
        shutil.rmtree(vid_dir, ignore_errors=True)

def main():
    # Check for missing dependencies
    if missing_deps:
        print(f"[red]Error: Faltan dependencias requeridas: {', '.join(missing_deps)}[/red]")
        print(f"[yellow]Instala con: pip install {' '.join(missing_deps)}[/yellow]")
        sys.exit(1)

    args = parse_args(sys.argv)
    url = args["url"]
    outdir: Path = args["outdir"]
    outdir.mkdir(parents=True, exist_ok=True)
    clips_dir = outdir / "clips"
    clips_dir.mkdir(exist_ok=True)

    print(f"[cyan]CUDA disponible:[/cyan] {HAS_CUDA} — Whisper/Demucs usarán [bold]{'GPU' if HAS_CUDA else 'CPU'}[/bold]")
    print(f"[cyan]Parámetros:[/cyan] min={MIN_SEC}s max={MAX_SEC}s sr={TARGET_SR} lang={LANG_SUBS} model={WHISPER_MODEL} demucs={USE_DEMUCS} denoise={USE_DENOISE}")

    # Enumerar videos (video único, playlist o canal)
    targets = list_targets(url)
    print(f"[green]{len(targets)}[/green] videos para procesar")

    meta_rows: List[Dict[str, Any]] = []
    report_rows: List[Dict[str, Any]] = []

    for vurl in tqdm(targets, desc="Procesando videos"):
        try:
            process_one_video(vurl, clips_dir, meta_rows, report_rows)
        except Exception as e:
            print(f"[red]Error procesando {vurl}: {e}[/red]")

    # Guardar CSVs
    meta_path = outdir / "metadata.csv"
    rep_path = outdir / "reporte_revision.csv"
    pd.DataFrame(meta_rows).to_csv(meta_path, index=False)
    pd.DataFrame(report_rows).to_csv(rep_path, index=False)

    print(f"[bold green]Listo[/bold green]. Clips: {len(meta_rows)} | Marcas revisión: {len(report_rows)}")
    print(f"- {meta_path}")
    print(f"- {rep_path}")

if __name__ == "__main__":
    main()
