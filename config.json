{"audio": {"min_duration_sec": 0.8, "max_duration_sec": 12.0, "sample_rate": 16000, "loudnorm": true, "loudnorm_params": {"I": -23, "LRA": 7, "TP": -2}}, "transcription": {"language": "es", "whisper_model": "small", "prefer_manual_subs": true}, "processing": {"use_demucs": false, "use_denoise": false, "arnndn_model_path": "./rnnoise-models/mastering.rnnn"}, "output": {"clips_subdir": "clips", "metadata_filename": "metadata.csv", "report_filename": "reporte_revision.csv"}, "quality_filters": {"detect_numbers": true, "detect_symbols": true, "symbol_regex": "[€$%#@−–—_=+<>^~|\\\\/*\\[\\]{}()§°ºª♂♀©®™☎✉✓✔★☆•♪♫¿¡]"}, "download": {"max_retries": 3, "retry_delay_sec": 5, "temp_dir": ".work"}}