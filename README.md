# Canal a Dataset

Un script de Python para crear datasets de audio con subtítulos a partir de canales, playlists o videos de YouTube.

## Características

- Descarga audio de videos de YouTube (canales completos, playlists o videos individuales)
- Extrae subtítulos existentes o genera transcripciones automáticas con Whisper
- Segmenta el audio en clips basados en los subtítulos
- Aplica procesamiento de audio (normalización, reducción de ruido, separación de voz)
- Genera metadatos en formato CSV
- Soporte para GPU (CUDA) automático para Whisper y Demucs
- Reportes de clips que requieren revisión (contienen números o símbolos especiales)

## Requisitos del Sistema

### Dependencias del Sistema
```bash
# Ubuntu/Debian
sudo apt-get install -y ffmpeg

# macOS
brew install ffmpeg

# Windows
# Descargar ffmpeg desde https://ffmpeg.org/download.html
```

### Dependencias de Python
```bash
pip install -r requirements.txt
```

## Uso

### Sintaxis Básica
```bash
./canal_a_dataset.py <URL_CANAL_O_PLAYLIST_O_VIDEO> <DIRECTORIO_SALIDA> [opciones]
```

### Ejemplos

#### Procesar un video individual
```bash
./canal_a_dataset.py "https://www.youtube.com/watch?v=VIDEO_ID" ./dataset_output
```

#### Procesar una playlist completa
```bash
./canal_a_dataset.py "https://www.youtube.com/playlist?list=PLAYLIST_ID" ./dataset_output
```

#### Procesar un canal completo
```bash
./canal_a_dataset.py "https://www.youtube.com/@canal_name" ./dataset_output
```

#### Con opciones avanzadas
```bash
./canal_a_dataset.py "https://www.youtube.com/@canal_name" ./dataset_output \
    --demucs \
    --denoise \
    --min-sec 1.0 \
    --max-sec 15.0 \
    --sr 22050 \
    --lang en \
    --model medium
```

## Opciones

| Opción | Descripción | Valor por defecto |
|--------|-------------|-------------------|
| `--demucs` | Usar Demucs para separación de voz | Desactivado |
| `--denoise` | Aplicar reducción de ruido con arnndn | Desactivado |
| `--min-sec` | Duración mínima de clips (segundos) | 0.8 |
| `--max-sec` | Duración máxima de clips (segundos) | 12.0 |
| `--sr` | Frecuencia de muestreo objetivo | 16000 |
| `--lang` | Idioma para subtítulos/Whisper | es |
| `--model` | Modelo de Whisper (tiny/base/small/medium/large) | small |

## Estructura de Salida

```
dataset_output/
├── clips/                    # Clips de audio segmentados
│   ├── VIDEO_ID_0001.wav
│   ├── VIDEO_ID_0002.wav
│   └── ...
├── metadata.csv             # Metadatos de todos los clips
└── reporte_revision.csv     # Clips que requieren revisión manual
```

### Formato de metadata.csv

| Columna | Descripción |
|---------|-------------|
| file | Nombre del archivo de audio |
| text | Texto transcrito |
| start | Tiempo de inicio (segundos) |
| end | Tiempo de fin (segundos) |
| video_url | URL del video original |
| video_id | ID del video de YouTube |
| device | Dispositivo usado (cuda/cpu) |

### Formato de reporte_revision.csv

Contiene clips que incluyen números o símbolos especiales que pueden requerir revisión manual.

## Configuración Avanzada

### Reducción de Ruido
Para usar `--denoise`, coloca un modelo RNNoise en:
```
./rnnoise-models/mastering.rnnn
```

### Optimización GPU
El script detecta automáticamente CUDA y lo usa para:
- Whisper (transcripción)
- Demucs (separación de voz)

## Pruebas

Ejecutar las pruebas unitarias:
```bash
python test_canal_dataset.py
```

## Solución de Problemas

### Error: "No se obtuvo WAV"
- Verifica que yt-dlp esté instalado y actualizado
- Comprueba que el video sea accesible públicamente

### Error: "Whisper no generó VTT"
- Verifica que openai-whisper esté instalado correctamente
- Comprueba que haya suficiente espacio en disco

### Rendimiento lento
- Usa `--model tiny` o `--model base` para transcripción más rápida
- Asegúrate de tener CUDA instalado para aceleración GPU
- Considera usar `--max-sec` más pequeño para clips más cortos

## Contribuir

1. Fork el repositorio
2. Crea una rama para tu feature (`git checkout -b feature/nueva-caracteristica`)
3. Commit tus cambios (`git commit -am 'Añadir nueva característica'`)
4. Push a la rama (`git push origin feature/nueva-caracteristica`)
5. Crea un Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo LICENSE para más detalles.
