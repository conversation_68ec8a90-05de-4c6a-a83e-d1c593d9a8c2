#!/bin/bash
# Script de prueba específico para YouTube Shorts con segmentación por silencios

set -e

echo "🎬 Prueba con YouTube Shorts - Segmentación por Silencios"
echo "========================================================="

# URL del video de prueba
VIDEO_URL="https://www.youtube.com/shorts/0HM-BJDjTIU"
OUTPUT_DIR="./test_shorts_output"

echo "📹 Video: $VIDEO_URL"
echo "📁 Salida: $OUTPUT_DIR"
echo ""

# Limpiar directorio de salida si existe
if [ -d "$OUTPUT_DIR" ]; then
    echo "🧹 Limpiando directorio anterior..."
    rm -rf "$OUTPUT_DIR"
fi

echo "🚀 Ejecutando con segmentación por silencios..."
echo ""

# Comando con segmentación por silencios optimizada para Shorts
./canal_a_dataset.py "$VIDEO_URL" "$OUTPUT_DIR" \
    --silence \
    --silence-thresh -35 \
    --silence-min 0.3 \
    --min-sec 1.0 \
    --max-sec 8.0 \
    --sr 16000

echo ""
echo "✅ Procesamiento completado!"
echo ""

# Mostrar resultados
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Resultados:"
    echo "=============="
    
    # Contar clips generados
    if [ -d "$OUTPUT_DIR/clips" ]; then
        CLIP_COUNT=$(ls -1 "$OUTPUT_DIR/clips"/*.wav 2>/dev/null | wc -l)
        echo "🎵 Clips generados: $CLIP_COUNT"
        
        if [ $CLIP_COUNT -gt 0 ]; then
            echo "📝 Primeros clips:"
            ls -la "$OUTPUT_DIR/clips" | head -6
        fi
    fi
    
    # Mostrar metadatos si existen
    if [ -f "$OUTPUT_DIR/metadata.csv" ]; then
        echo ""
        echo "📋 Metadatos (primeras 5 líneas):"
        head -5 "$OUTPUT_DIR/metadata.csv"
    fi
    
    # Analizar con la herramienta de análisis
    if [ -f "./analyze_dataset.py" ]; then
        echo ""
        echo "📈 Análisis detallado:"
        echo "====================="
        ./analyze_dataset.py "$OUTPUT_DIR"
    fi
    
else
    echo "❌ No se generó el directorio de salida"
    exit 1
fi

echo ""
echo "🎯 Comandos útiles:"
echo "=================="
echo "# Ver todos los clips:"
echo "ls -la $OUTPUT_DIR/clips/"
echo ""
echo "# Reproducir un clip (si tienes ffplay):"
echo "ffplay $OUTPUT_DIR/clips/\$(ls $OUTPUT_DIR/clips/ | head -1)"
echo ""
echo "# Ver metadatos completos:"
echo "cat $OUTPUT_DIR/metadata.csv"
echo ""
echo "# Análisis detallado:"
echo "./analyze_dataset.py $OUTPUT_DIR"
