# Canal a Dataset - Resumen del Proyecto

## 📋 Descripción General

Este proyecto proporciona una herramienta completa para crear datasets de audio con transcripciones a partir de contenido de YouTube. Es especialmente útil para entrenar modelos de reconocimiento de voz, análisis de audio, o crear corpus de texto hablado.

## 🗂️ Estructura del Proyecto

```
dataset_senor/
├── canal_a_dataset.py      # Script principal
├── requirements.txt        # Dependencias de Python
├── setup.sh               # Script de configuración automática
├── config.json            # Configuración por defecto
├── test_canal_dataset.py   # Pruebas unitarias
├── analyze_dataset.py      # Herramienta de análisis de datasets
├── example_usage.py        # Ejemplos de uso interactivos
├── README.md              # Documentación completa
└── PROJECT_SUMMARY.md     # Este archivo
```

## 🚀 Características Principales

### Funcionalidades Core
- ✅ Descarga de audio desde YouTube (videos, playlists, canales)
- ✅ Extracción de subtítulos existentes o generación con Whisper
- ✅ Segmentación automática basada en subtítulos
- ✅ Procesamiento de audio (normalización, mono, resampling)
- ✅ Generación de metadatos en CSV
- ✅ Detección automática de GPU (CUDA)

### Funcionalidades Avanzadas
- ✅ Separación de voz con Demucs (opcional)
- ✅ Reducción de ruido con RNNoise (opcional)
- ✅ Filtrado por duración de clips
- ✅ Detección de clips que requieren revisión manual
- ✅ Soporte multi-idioma
- ✅ Configuración flexible via CLI

### Herramientas Auxiliares
- ✅ Script de configuración automática (`setup.sh`)
- ✅ Análisis de datasets generados (`analyze_dataset.py`)
- ✅ Ejemplos interactivos (`example_usage.py`)
- ✅ Pruebas unitarias (`test_canal_dataset.py`)

## 🛠️ Instalación Rápida

```bash
# Clonar o descargar el proyecto
cd dataset_senor

# Ejecutar configuración automática
./setup.sh

# O instalación manual
pip install -r requirements.txt
```

## 📊 Uso Típico

### Caso 1: Video Individual
```bash
./canal_a_dataset.py "https://youtube.com/watch?v=VIDEO_ID" ./mi_dataset
```

### Caso 2: Playlist Completa
```bash
./canal_a_dataset.py "https://youtube.com/playlist?list=PLAYLIST_ID" ./mi_dataset --model base
```

### Caso 3: Configuración Avanzada
```bash
./canal_a_dataset.py "URL" ./dataset \
    --demucs \
    --min-sec 2.0 \
    --max-sec 10.0 \
    --sr 22050 \
    --lang en
```

## 📈 Análisis de Resultados

```bash
# Analizar dataset generado
./analyze_dataset.py ./mi_dataset
```

Esto proporciona:
- Estadísticas generales (clips, duración, videos)
- Análisis de texto (caracteres, palabras)
- Clips marcados para revisión
- Información de archivos de audio

## 🔧 Configuración Avanzada

### GPU/CUDA
- Detección automática de CUDA
- Aceleración para Whisper y Demucs
- Fallback a CPU si no hay GPU

### Modelos de Whisper
- `tiny`: Más rápido, menos preciso
- `base`: Balance velocidad/precisión
- `small`: Recomendado por defecto
- `medium`: Más preciso, más lento
- `large`: Máxima precisión

### Filtros de Audio
- Normalización de volumen (loudnorm)
- Reducción de ruido (RNNoise)
- Separación de voz (Demucs)
- Conversión a mono y resampling

## 📋 Salida del Dataset

### Estructura Típica
```
mi_dataset/
├── clips/                    # Archivos de audio segmentados
│   ├── VIDEO_ID_0001.wav
│   ├── VIDEO_ID_0002.wav
│   └── ...
├── metadata.csv             # Metadatos completos
└── reporte_revision.csv     # Clips para revisar manualmente
```

### Formato de Metadatos
| Campo | Descripción |
|-------|-------------|
| file | Nombre del archivo de audio |
| text | Transcripción del segmento |
| start/end | Timestamps en segundos |
| video_url | URL del video original |
| video_id | ID único del video |
| device | Dispositivo usado (cuda/cpu) |

## 🧪 Testing

```bash
# Ejecutar todas las pruebas
python test_canal_dataset.py -v

# Verificar dependencias
python canal_a_dataset.py
```

## 🎯 Casos de Uso Recomendados

### Para Investigación
- Corpus de habla natural
- Análisis fonético/prosódico
- Estudios de variación lingüística

### Para Machine Learning
- Entrenamiento de ASR (Automatic Speech Recognition)
- Text-to-Speech datasets
- Análisis de sentimientos en audio

### Para Educación
- Materiales de aprendizaje de idiomas
- Análisis de pronunciación
- Corpus para lingüística computacional

## ⚠️ Consideraciones Importantes

### Legales
- Respetar términos de servicio de YouTube
- Verificar derechos de autor del contenido
- Uso educativo/investigación recomendado

### Técnicas
- Empezar con videos cortos para pruebas
- Monitorear espacio en disco
- Revisar clips marcados manualmente

### Rendimiento
- GPU recomendada para datasets grandes
- Ajustar parámetros según necesidades
- Considerar límites de ancho de banda

## 🔄 Flujo de Trabajo Recomendado

1. **Preparación**: Instalar dependencias con `setup.sh`
2. **Prueba**: Procesar un video corto primero
3. **Configuración**: Ajustar parámetros según resultados
4. **Procesamiento**: Ejecutar en dataset completo
5. **Análisis**: Usar `analyze_dataset.py` para estadísticas
6. **Revisión**: Verificar clips marcados en reporte
7. **Limpieza**: Filtrar/corregir según necesidades

## 📞 Soporte y Contribuciones

- Reportar bugs via issues
- Contribuciones via pull requests
- Documentación en README.md
- Ejemplos en example_usage.py

---

**Versión**: 1.0  
**Última actualización**: 2025-09-20  
**Compatibilidad**: Python 3.8+, Linux/macOS/Windows
