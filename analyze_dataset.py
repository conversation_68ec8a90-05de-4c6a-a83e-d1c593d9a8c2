#!/usr/bin/env python3
"""
analyze_dataset.py - Analiza un dataset creado por canal_a_dataset.py
Proporciona estadísticas útiles sobre el dataset generado.
"""

import sys
import json
from pathlib import Path
from collections import Counter, defaultdict
import re

try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("⚠️  pandas no disponible. Instala con: pip install pandas")

try:
    from rich import print
    from rich.table import Table
    from rich.console import Console
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    def print(*args, **kwargs):
        import builtins
        builtins.print(*args, **kwargs)

def analyze_metadata(metadata_path: Path) -> dict:
    """Analiza el archivo metadata.csv y retorna estadísticas."""
    if not HAS_PANDAS:
        print("❌ pandas requerido para análisis")
        return {}
    
    df = pd.read_csv(metadata_path)
    
    # Estadísticas básicas
    total_clips = len(df)
    total_duration = (df['end'] - df['start']).sum()
    avg_duration = (df['end'] - df['start']).mean()
    
    # Estadísticas por video
    videos = df['video_id'].nunique()
    clips_per_video = df.groupby('video_id').size()
    
    # Estadísticas de texto
    text_lengths = df['text'].str.len()
    word_counts = df['text'].str.split().str.len()
    
    # Idiomas detectados (simple heurística)
    spanish_chars = df['text'].str.contains('[ñáéíóúü]', case=False, na=False).sum()
    english_chars = df['text'].str.contains('[a-z]', case=False, na=False).sum()
    
    return {
        'total_clips': total_clips,
        'total_duration_sec': total_duration,
        'total_duration_hours': total_duration / 3600,
        'avg_duration_sec': avg_duration,
        'unique_videos': videos,
        'clips_per_video': {
            'mean': clips_per_video.mean(),
            'median': clips_per_video.median(),
            'min': clips_per_video.min(),
            'max': clips_per_video.max()
        },
        'text_stats': {
            'avg_chars': text_lengths.mean(),
            'avg_words': word_counts.mean(),
            'min_chars': text_lengths.min(),
            'max_chars': text_lengths.max()
        },
        'language_hints': {
            'spanish_clips': spanish_chars,
            'english_clips': english_chars
        }
    }

def analyze_report(report_path: Path) -> dict:
    """Analiza el archivo de reporte de revisión."""
    if not HAS_PANDAS or not report_path.exists():
        return {}
    
    df = pd.read_csv(report_path)
    
    return {
        'total_flagged': len(df),
        'has_numbers': df['has_number'].sum(),
        'has_symbols': df['has_symbol'].sum(),
        'both': ((df['has_number'] == 1) & (df['has_symbol'] == 1)).sum()
    }

def analyze_audio_files(clips_dir: Path) -> dict:
    """Analiza los archivos de audio en el directorio clips."""
    if not clips_dir.exists():
        return {}
    
    wav_files = list(clips_dir.glob("*.wav"))
    total_files = len(wav_files)
    
    if total_files == 0:
        return {'total_files': 0}
    
    # Calcular tamaño total
    total_size = sum(f.stat().st_size for f in wav_files)
    avg_size = total_size / total_files
    
    return {
        'total_files': total_files,
        'total_size_mb': total_size / (1024 * 1024),
        'avg_size_kb': avg_size / 1024
    }

def print_analysis(dataset_dir: Path):
    """Imprime análisis completo del dataset."""
    metadata_path = dataset_dir / "metadata.csv"
    report_path = dataset_dir / "reporte_revision.csv"
    clips_dir = dataset_dir / "clips"
    
    if HAS_RICH:
        console = Console()
        console.print(f"\n🔍 [bold]Análisis del Dataset:[/bold] {dataset_dir}")
        console.print("=" * 60)
    else:
        print(f"\n🔍 Análisis del Dataset: {dataset_dir}")
        print("=" * 60)
    
    # Análisis de metadatos
    if metadata_path.exists():
        stats = analyze_metadata(metadata_path)
        if stats:
            if HAS_RICH:
                table = Table(title="📊 Estadísticas Generales")
                table.add_column("Métrica", style="cyan")
                table.add_column("Valor", style="green")
                
                table.add_row("Total de clips", f"{stats['total_clips']:,}")
                table.add_row("Videos únicos", f"{stats['unique_videos']:,}")
                table.add_row("Duración total", f"{stats['total_duration_hours']:.2f} horas")
                table.add_row("Duración promedio", f"{stats['avg_duration_sec']:.2f} segundos")
                table.add_row("Clips por video (promedio)", f"{stats['clips_per_video']['mean']:.1f}")
                table.add_row("Caracteres promedio", f"{stats['text_stats']['avg_chars']:.1f}")
                table.add_row("Palabras promedio", f"{stats['text_stats']['avg_words']:.1f}")
                
                console.print(table)
            else:
                print("\n📊 Estadísticas Generales:")
                print(f"  Total de clips: {stats['total_clips']:,}")
                print(f"  Videos únicos: {stats['unique_videos']:,}")
                print(f"  Duración total: {stats['total_duration_hours']:.2f} horas")
                print(f"  Duración promedio: {stats['avg_duration_sec']:.2f} segundos")
                print(f"  Clips por video (promedio): {stats['clips_per_video']['mean']:.1f}")
    else:
        print("❌ metadata.csv no encontrado")
    
    # Análisis de reporte
    if report_path.exists():
        report_stats = analyze_report(report_path)
        if report_stats:
            if HAS_RICH:
                table = Table(title="⚠️  Clips Marcados para Revisión")
                table.add_column("Tipo", style="yellow")
                table.add_column("Cantidad", style="red")
                
                table.add_row("Total marcados", f"{report_stats['total_flagged']:,}")
                table.add_row("Con números", f"{report_stats['has_numbers']:,}")
                table.add_row("Con símbolos", f"{report_stats['has_symbols']:,}")
                table.add_row("Con ambos", f"{report_stats['both']:,}")
                
                console.print(table)
            else:
                print("\n⚠️  Clips Marcados para Revisión:")
                print(f"  Total marcados: {report_stats['total_flagged']:,}")
                print(f"  Con números: {report_stats['has_numbers']:,}")
                print(f"  Con símbolos: {report_stats['has_symbols']:,}")
    else:
        print("ℹ️  No hay archivo de reporte (reporte_revision.csv)")
    
    # Análisis de archivos de audio
    audio_stats = analyze_audio_files(clips_dir)
    if audio_stats.get('total_files', 0) > 0:
        if HAS_RICH:
            table = Table(title="🎵 Archivos de Audio")
            table.add_column("Métrica", style="blue")
            table.add_column("Valor", style="green")
            
            table.add_row("Archivos de audio", f"{audio_stats['total_files']:,}")
            table.add_row("Tamaño total", f"{audio_stats['total_size_mb']:.1f} MB")
            table.add_row("Tamaño promedio", f"{audio_stats['avg_size_kb']:.1f} KB")
            
            console.print(table)
        else:
            print("\n🎵 Archivos de Audio:")
            print(f"  Archivos de audio: {audio_stats['total_files']:,}")
            print(f"  Tamaño total: {audio_stats['total_size_mb']:.1f} MB")
            print(f"  Tamaño promedio: {audio_stats['avg_size_kb']:.1f} KB")
    else:
        print("❌ No se encontraron archivos de audio en clips/")

def main():
    if len(sys.argv) != 2:
        print("Uso: python analyze_dataset.py <DIRECTORIO_DATASET>")
        sys.exit(1)
    
    dataset_dir = Path(sys.argv[1])
    if not dataset_dir.exists():
        print(f"❌ Directorio no existe: {dataset_dir}")
        sys.exit(1)
    
    print_analysis(dataset_dir)

if __name__ == "__main__":
    main()
