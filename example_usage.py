#!/usr/bin/env python3
"""
Ejemplo de uso de canal_a_dataset.py
Muestra diferentes formas de usar el script para crear datasets.
"""

import subprocess
import sys
from pathlib import Path

def run_example(description, command):
    """Ejecuta un comando de ejemplo y muestra la descripción."""
    print(f"\n{'='*60}")
    print(f"EJEMPLO: {description}")
    print(f"{'='*60}")
    print(f"Comando: {' '.join(command)}")
    print("\nPresiona Enter para ejecutar o Ctrl+C para saltar...")
    try:
        input()
        subprocess.run(command, check=True)
    except KeyboardInterrupt:
        print("\nSaltando ejemplo...")
    except subprocess.CalledProcessError as e:
        print(f"Error ejecutando comando: {e}")

def main():
    print("🎬 Ejemplos de uso de canal_a_dataset.py")
    print("=" * 50)
    
    # Verificar que el script principal existe
    if not Path("canal_a_dataset.py").exists():
        print("❌ Error: canal_a_dataset.py no encontrado en el directorio actual")
        sys.exit(1)
    
    print("\nEste script te guiará a través de diferentes ejemplos de uso.")
    print("Cada ejemplo te mostrará el comando y te permitirá ejecutarlo.")
    print("\nNOTA: Necesitarás URLs reales de YouTube para que funcionen.")
    
    # Ejemplo 1: Video individual básico
    run_example(
        "Procesar un video individual (básico)",
        [
            "./canal_a_dataset.py",
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Roll como ejemplo
            "./dataset_ejemplo1"
        ]
    )
    
    # Ejemplo 2: Video con segmentación por silencios (ideal para Shorts/música)
    run_example(
        "Video con segmentación por silencios (recomendado para Shorts)",
        [
            "./canal_a_dataset.py",
            "https://www.youtube.com/shorts/0HM-BJDjTIU",
            "./dataset_ejemplo2",
            "--silence",
            "--silence-thresh", "-35",
            "--silence-min", "0.3",
            "--min-sec", "1.0",
            "--max-sec", "8.0"
        ]
    )

    # Ejemplo 3: Video con opciones avanzadas
    run_example(
        "Video con separación de voz y configuración personalizada",
        [
            "./canal_a_dataset.py",
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "./dataset_ejemplo3",
            "--demucs",
            "--min-sec", "2.0",
            "--max-sec", "8.0",
            "--sr", "22050",
            "--model", "base"
        ]
    )
    
    # Ejemplo 4: Playlist
    run_example(
        "Procesar una playlist completa",
        [
            "./canal_a_dataset.py",
            "https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMHjMZOz59Oq3MJykr",  # Ejemplo de playlist
            "./dataset_playlist",
            "--lang", "en",
            "--model", "small"
        ]
    )
    
    # Ejemplo 5: Canal completo (cuidado - puede ser mucho contenido)
    print(f"\n{'='*60}")
    print("EJEMPLO: Procesar un canal completo (¡CUIDADO!)")
    print(f"{'='*60}")
    print("Este ejemplo puede descargar MUCHOS videos.")
    print("Solo úsalo con canales pequeños o para pruebas.")
    print("\nComando de ejemplo:")
    print("./canal_a_dataset.py 'https://www.youtube.com/@channelname' ./dataset_canal --max-sec 5.0")
    
    # Ejemplo 6: Solo mostrar ayuda
    run_example(
        "Mostrar ayuda del script",
        ["./canal_a_dataset.py"]
    )
    
    print(f"\n{'='*60}")
    print("CONSEJOS ADICIONALES")
    print(f"{'='*60}")
    print("""
1. Empieza con videos cortos para probar la configuración
2. Para YouTube Shorts/música: usa --silence en lugar de subtítulos
3. Usa --model tiny o --model base para transcripción más rápida
4. Ajusta --silence-thresh según el ruido de fondo (-30 a -50 dB)
5. Ajusta --min-sec y --max-sec según tus necesidades
6. Revisa el archivo reporte_revision.csv para clips que necesiten revisión manual
7. El directorio clips/ contendrá todos los archivos de audio segmentados
8. metadata.csv tiene toda la información de cada clip

SEGMENTACIÓN:
- --silence: Ideal para música, Shorts, audio sin subtítulos claros
- Sin --silence: Ideal para habla, conferencias, contenido educativo

Estructura de salida típica:
dataset_output/
├── clips/
│   ├── VIDEO_ID_0001.wav
│   ├── VIDEO_ID_0002.wav
│   └── ...
├── metadata.csv
└── reporte_revision.csv
""")

if __name__ == "__main__":
    main()
