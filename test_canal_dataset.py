#!/usr/bin/env python3
"""
Test script for canal_a_dataset.py
Tests basic functionality without requiring actual YouTube downloads.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys
import os
import re

# Add the current directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock the missing dependencies before importing
sys.modules['webvtt'] = MagicMock()
sys.modules['pandas'] = MagicMock()
sys.modules['tqdm'] = MagicMock()
sys.modules['rich'] = MagicMock()

import canal_a_dataset as cad


class TestCanalDataset(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_dir = Path(tempfile.mkdtemp())
        
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_parse_args_basic(self):
        """Test basic argument parsing."""
        # Reset global variables first
        cad.USE_DEMUCS = False
        cad.USE_DENOISE = False
        cad.MIN_SEC = 0.8
        cad.MAX_SEC = 12.0
        cad.TARGET_SR = 16000
        cad.LANG_SUBS = "es"
        cad.WHISPER_MODEL = "small"

        argv = ["script.py", "https://youtube.com/watch?v=test", "/tmp/output"]
        result = cad.parse_args(argv)
        self.assertEqual(result["url"], "https://youtube.com/watch?v=test")
        self.assertEqual(result["outdir"], Path("/tmp/output"))
    
    def test_parse_args_with_options(self):
        """Test argument parsing with options."""
        # Reset global variables first
        cad.USE_DEMUCS = False
        cad.USE_DENOISE = False
        cad.MIN_SEC = 0.8
        cad.MAX_SEC = 12.0
        cad.TARGET_SR = 16000
        cad.LANG_SUBS = "es"
        cad.WHISPER_MODEL = "small"

        argv = [
            "script.py",
            "https://youtube.com/watch?v=test",
            "/tmp/output",
            "--demucs",
            "--denoise",
            "--min-sec", "1.0",
            "--max-sec", "15.0",
            "--sr", "22050",
            "--lang", "en",
            "--model", "medium"
        ]
        result = cad.parse_args(argv)
        self.assertEqual(result["url"], "https://youtube.com/watch?v=test")
        self.assertEqual(cad.USE_DEMUCS, True)
        self.assertEqual(cad.USE_DENOISE, True)
        self.assertEqual(cad.MIN_SEC, 1.0)
        self.assertEqual(cad.MAX_SEC, 15.0)
        self.assertEqual(cad.TARGET_SR, 22050)
        self.assertEqual(cad.LANG_SUBS, "en")
        self.assertEqual(cad.WHISPER_MODEL, "medium")
    
    def test_ffmpeg_base_filters(self):
        """Test ffmpeg filter generation."""
        # Reset global variables
        cad.LOUDNORM = True
        cad.USE_DENOISE = False
        cad.TARGET_SR = 16000
        
        filters = cad.ffmpeg_base_filters()
        self.assertIn("loudnorm", filters)
        self.assertIn("aformat=channel_layouts=mono", filters)
        self.assertIn("aresample=16000", filters)
    
    def test_hhmmss_to_s_conversion(self):
        """Test time conversion in parse_vtt_to_segments."""
        # Create a mock VTT file content
        vtt_content = """WEBVTT

1
00:00:01.000 --> 00:00:03.500
Hello world

2
00:01:30.250 --> 00:01:35.750
This is a test
"""
        vtt_path = self.test_dir / "test.vtt"
        vtt_path.write_text(vtt_content)
        
        # Mock webvtt.read to avoid dependency issues in testing
        with patch('canal_a_dataset.webvtt') as mock_webvtt:
            mock_caption1 = MagicMock()
            mock_caption1.start = "00:00:01.000"
            mock_caption1.end = "00:00:03.500"
            mock_caption1.text = "Hello world"
            
            mock_caption2 = MagicMock()
            mock_caption2.start = "00:01:30.250"
            mock_caption2.end = "00:01:35.750"
            mock_caption2.text = "This is a test"
            
            mock_webvtt.read.return_value = [mock_caption1, mock_caption2]
            
            segments = cad.parse_vtt_to_segments(vtt_path)
            
            self.assertEqual(len(segments), 2)
            self.assertEqual(segments[0]["start"], 1.0)
            self.assertEqual(segments[0]["end"], 3.5)
            self.assertEqual(segments[0]["text"], "Hello world")
            self.assertEqual(segments[1]["start"], 90.25)
            self.assertEqual(segments[1]["end"], 95.75)
    
    def test_regex_patterns(self):
        """Test the regex patterns for numbers and symbols."""
        # Test number detection
        self.assertTrue(cad.RX_NUM.search("Hello 123 world"))
        self.assertFalse(cad.RX_NUM.search("Hello world"))
        
        # Test symbol detection
        self.assertTrue(cad.RX_SYM.search("Hello @ world"))
        self.assertTrue(cad.RX_SYM.search("Price: $50"))
        self.assertFalse(cad.RX_SYM.search("Hello world"))
    
    def test_which_function(self):
        """Test the which function for finding binaries."""
        # Test with a command that should exist
        result = cad.which("python3")
        self.assertIsNotNone(result)
        
        # Test with a command that shouldn't exist
        result = cad.which("nonexistent_command_12345", "default_value")
        self.assertEqual(result, "default_value")


if __name__ == "__main__":
    # Reset global variables before running tests
    cad.USE_DEMUCS = False
    cad.USE_DENOISE = False
    cad.MIN_SEC = 0.8
    cad.MAX_SEC = 12.0
    cad.TARGET_SR = 16000
    cad.LANG_SUBS = "es"
    cad.WHISPER_MODEL = "small"
    cad.LOUDNORM = True
    
    unittest.main()
