#!/bin/bash
# Setup script for canal_a_dataset.py

set -e

echo "🚀 Configurando canal_a_dataset..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 no está instalado. Por favor instálalo primero."
    exit 1
fi

echo "✅ Python 3 encontrado: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null && ! command -v pip &> /dev/null; then
    echo "❌ pip no está instalado. Por favor instálalo primero."
    exit 1
fi

# Use pip3 if available, otherwise pip
PIP_CMD="pip3"
if ! command -v pip3 &> /dev/null; then
    PIP_CMD="pip"
fi

echo "✅ pip encontrado: $($PIP_CMD --version)"

# Check if ffmpeg is installed
if ! command -v ffmpeg &> /dev/null; then
    echo "⚠️  ffmpeg no está instalado."
    echo "   Ubuntu/Debian: sudo apt-get install -y ffmpeg"
    echo "   macOS: brew install ffmpeg"
    echo "   Windows: <PERSON><PERSON><PERSON> desde https://ffmpeg.org/download.html"
    read -p "¿Continuar sin ffmpeg? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo "✅ ffmpeg encontrado: $(ffmpeg -version | head -n1)"
fi

# Check if yt-dlp is installed
if ! command -v yt-dlp &> /dev/null; then
    echo "📦 Instalando yt-dlp..."
    $PIP_CMD install yt-dlp
else
    echo "✅ yt-dlp encontrado: $(yt-dlp --version)"
fi

# Install Python dependencies
echo "📦 Instalando dependencias de Python..."
$PIP_CMD install -r requirements.txt

# Check if CUDA is available (optional)
echo "🔍 Verificando soporte CUDA..."
python3 -c "
try:
    import torch
    if torch.cuda.is_available():
        print('✅ CUDA disponible - GPU será usada para Whisper/Demucs')
        print(f'   Dispositivos: {torch.cuda.device_count()}')
        for i in range(torch.cuda.device_count()):
            print(f'   - {torch.cuda.get_device_name(i)}')
    else:
        print('⚠️  CUDA no disponible - se usará CPU')
except ImportError:
    print('⚠️  PyTorch no instalado - se usará CPU')
"

# Make script executable
chmod +x canal_a_dataset.py
echo "✅ Script hecho ejecutable"

# Create rnnoise-models directory for denoising (optional)
mkdir -p rnnoise-models
echo "📁 Directorio rnnoise-models creado (para --denoise)"

echo ""
echo "🎉 ¡Configuración completada!"
echo ""
echo "Uso básico:"
echo "  ./canal_a_dataset.py 'https://youtube.com/watch?v=VIDEO_ID' ./output"
echo ""
echo "Con opciones:"
echo "  ./canal_a_dataset.py 'URL' ./output --demucs --min-sec 1.0 --max-sec 10.0"
echo ""
echo "Para más información:"
echo "  ./canal_a_dataset.py"
echo "  cat README.md"
